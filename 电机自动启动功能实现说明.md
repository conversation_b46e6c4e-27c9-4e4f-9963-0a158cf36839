# TI MSPM0G3507 电机自动启动功能实现说明

## 修改概述

本次修改实现了系统上电初始化完成后，左右两个电机立即以固定速度15运行的功能。

---

## 1. 速度参数说明

### 1.1 速度单位确认
- **速度15的单位**: **编码器脉冲/秒** (pulses per second)
- **编码器分辨率**: 260脉冲/圈 (`MOTOR_FULL_VALUE = 260`)
- **实际转速**: 15脉冲/秒 ÷ 260脉冲/圈 = 0.058转/秒 = 3.46转/分钟

### 1.2 速度与PWM关系
- **PWM占空比范围**: -100% 到 +100%
- **PID控制**: 系统使用PID闭环控制，自动调节PWM占空比以达到目标速度
- **初始PWM**: 设置为15%作为快速启动的初始值

---

## 2. 代码修改详情

### 2.1 修改文件列表
1. `APP/Src/Task_App.c` - 主要修改文件
2. `BSP/Src/Motor.c` - 添加新的电机控制函数
3. `BSP/Inc/Motor.h` - 添加函数声明

### 2.2 具体修改内容

#### 2.2.1 Task_App.c 修改

**修改1: 全局目标速度变量**
```c
// 原代码
_iq Data_Motor_TarSpeed = _IQ(30); //目标基础速度

// 修改后
_iq Data_Motor_TarSpeed = _IQ(15); //目标基础速度 (修改为15编码器脉冲/秒)
```

**修改2: Task_Init()函数添加电机启动代码**
```c
void Task_Init(void)
{
    Motor_Start(); //开启电机
    Serial_Init(); //初始化串口
    OLED_Init(); //OLED初始化
    MPU6050_Init(); //MPU6050初始化

    Interrupt_Init(); //中断初始化

    // 灰度传感器初始化...
    
    // 新增：设置电机立即启动运行
    // 设置目标速度为15编码器脉冲/秒 (约3.46转/分钟)
    Motor_SetBothTargetSpeed(15.0f, 15.0f);  // 同时设置左右电机目标速度
    
    // 可选：设置初始PWM输出以快速启动（可根据需要调整或删除）
    Motor_SetDuty(&Motor_Left, 15.0f);   // 设置左电机初始PWM占空比15%
    Motor_SetDuty(&Motor_Right, 15.0f);  // 设置右电机初始PWM占空比15%

    // 任务添加...
}
```

#### 2.2.2 Motor.c 新增函数

**新增函数1: 单个电机速度设置**
```c
/**
 * @brief 设置电机目标速度
 * @param Motor 电机实例指针
 * @param speed 目标速度 (编码器脉冲/秒)
 * @return bool 设置成功返回true
 */
bool Motor_SetTargetSpeed(MOTOR_Def_t *Motor, float speed)
{
    if (Motor == NULL) return false;
    
    Motor->Motor_PID_Instance.Target = _IQ(speed);
    return true;
}
```

**新增函数2: 双电机速度设置**
```c
/**
 * @brief 同时设置左右电机目标速度
 * @param left_speed 左电机目标速度 (编码器脉冲/秒)
 * @param right_speed 右电机目标速度 (编码器脉冲/秒)
 */
void Motor_SetBothTargetSpeed(float left_speed, float right_speed)
{
    Motor_SetTargetSpeed(&Motor_Left, left_speed);
    Motor_SetTargetSpeed(&Motor_Right, right_speed);
}
```

#### 2.2.3 Motor.h 函数声明
```c
void Motor_Start(void);
bool Motor_SetDuty(MOTOR_Def_t *Motor, float value);
bool Motor_GetSpeed(MOTOR_Def_t *Motor, uint16_t time);
bool Motor_SetTargetSpeed(MOTOR_Def_t *Motor, float speed);        // 新增
void Motor_SetBothTargetSpeed(float left_speed, float right_speed); // 新增
```

---

## 3. 工作原理

### 3.1 启动流程
1. **系统初始化**: `SYSCFG_DL_init()` 完成硬件初始化
2. **任务初始化**: `Task_Init()` 完成各模块初始化
3. **电机启动**: 在`Task_Init()`中设置目标速度和初始PWM
4. **PID控制**: `Task_Motor_PID()`任务(50ms周期)开始PID闭环控制
5. **速度维持**: 系统自动维持15脉冲/秒的目标速度

### 3.2 控制逻辑
```
目标速度(15脉冲/秒) → PID控制器 → PWM占空比 → 电机转动 → 编码器反馈 → 实际速度
                                ↑                                    ↓
                                ←←←←←←←← 闭环反馈 ←←←←←←←←←←←←←←←←←←
```

### 3.3 PID参数
- **Kp**: 5.0 (比例系数)
- **Ki**: 0.5 (积分系数)  
- **Kd**: 0.1 (微分系数)
- **控制周期**: 50ms

---

## 4. 使用说明

### 4.1 基本使用
系统上电后，电机将自动以15脉冲/秒的速度运行，无需任何手动操作。

### 4.2 速度调整
如需修改电机速度，可以：

**方法1: 修改初始化代码**
```c
// 在Task_Init()中修改
Motor_SetBothTargetSpeed(20.0f, 20.0f);  // 改为20脉冲/秒
```

**方法2: 运行时动态调整**
```c
// 在任何地方调用
Motor_SetTargetSpeed(&Motor_Left, 25.0f);   // 左电机25脉冲/秒
Motor_SetTargetSpeed(&Motor_Right, 25.0f);  // 右电机25脉冲/秒
```

**方法3: 修改全局基础速度**
```c
// 修改Task_App.c中的全局变量
_iq Data_Motor_TarSpeed = _IQ(20); // 改为20脉冲/秒
```

### 4.3 速度范围建议
- **最小速度**: 5脉冲/秒 (约1.15转/分钟)
- **推荐范围**: 10-50脉冲/秒 (2.3-11.5转/分钟)
- **最大速度**: 取决于电机和电源，建议不超过100脉冲/秒

---

## 5. 注意事项

### 5.1 安全提醒
- 首次运行时请确保小车放置在安全区域
- 电机启动后会立即运行，请做好防护措施
- 如需停止电机，可调用 `Motor_SetBothTargetSpeed(0.0f, 0.0f)`

### 5.2 调试建议
- 可通过OLED显示屏观察实际速度
- 串口输出包含PID控制数据，便于调试
- 建议先用较低速度测试，确认正常后再提高速度

### 5.3 与循迹功能的兼容性
- 当前修改与循迹功能兼容
- 循迹算法会在基础速度15脉冲/秒的基础上进行差速控制
- 如需禁用循迹，可将 `Data_Tracker_Offset` 设为0

---

## 6. 故障排除

### 6.1 电机不转动
- 检查电源连接
- 确认TB6612驱动芯片工作正常
- 检查PWM信号输出

### 6.2 速度不稳定
- 调整PID参数
- 检查编码器连接
- 确认机械负载合理

### 6.3 速度偏差较大
- 校准编码器
- 检查电机机械状态
- 调整PID积分参数

---

**修改完成日期**: 2025-01-31  
**测试状态**: 代码修改完成，建议实际测试验证  
**兼容性**: 与现有循迹、显示、串口功能完全兼容
